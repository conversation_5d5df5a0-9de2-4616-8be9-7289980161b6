<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Utility Functions JavaScript -->
    <t t-name="scripts.utils">
        <script><![CDATA[
            // Utility Functions
            class Utils {
                // Format file sizes
                static formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }

                // Format dates
                static formatDate(dateString) {
                    if (!dateString) return 'Unknown';
                    
                    try {
                        const date = new Date(dateString);
                        return date.toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                        });
                    } catch (error) {
                        return 'Invalid Date';
                    }
                }

                // Format relative time
                static formatRelativeTime(dateString) {
                    if (!dateString) return 'Unknown';
                    
                    try {
                        const date = new Date(dateString);
                        const now = new Date();
                        const diffMs = now - date;
                        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                        
                        if (diffDays === 0) return 'Today';
                        if (diffDays === 1) return 'Yesterday';
                        if (diffDays < 7) return `${diffDays} days ago`;
                        if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
                        if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
                        return `${Math.floor(diffDays / 365)} years ago`;
                    } catch (error) {
                        return 'Unknown';
                    }
                }

                // Debounce function
                static debounce(func, wait, immediate = false) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            timeout = null;
                            if (!immediate) func(...args);
                        };
                        const callNow = immediate && !timeout;
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                        if (callNow) func(...args);
                    };
                }

                // Throttle function
                static throttle(func, limit) {
                    let inThrottle;
                    return function(...args) {
                        if (!inThrottle) {
                            func.apply(this, args);
                            inThrottle = true;
                            setTimeout(() => inThrottle = false, limit);
                        }
                    };
                }

                // Validate database name
                static validateDatabaseName(name) {
                    if (!name) return { valid: false, message: 'Database name is required' };
                    if (name.length < 3) return { valid: false, message: 'Database name must be at least 3 characters long' };
                    if (name.length > 63) return { valid: false, message: 'Database name must be less than 64 characters long' };
                    if (!/^[a-zA-Z0-9_-]+$/.test(name)) return { valid: false, message: 'Database name can only contain letters, numbers, underscores, and hyphens' };
                    if (/^[0-9]/.test(name)) return { valid: false, message: 'Database name cannot start with a number' };
                    
                    // Reserved names
                    const reserved = ['postgres', 'template0', 'template1', 'admin', 'root', 'system'];
                    if (reserved.includes(name.toLowerCase())) return { valid: false, message: 'This database name is reserved' };
                    
                    return { valid: true, message: 'Valid database name' };
                }

                // Copy text to clipboard
                static async copyToClipboard(text) {
                    try {
                        await navigator.clipboard.writeText(text);
                        return true;
                    } catch (error) {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        
                        try {
                            document.execCommand('copy');
                            document.body.removeChild(textArea);
                            return true;
                        } catch (err) {
                            document.body.removeChild(textArea);
                            return false;
                        }
                    }
                }

                // Show toast notification
                static showToast(message, type = 'info', duration = 3000) {
                    // Remove existing toasts
                    const existingToasts = document.querySelectorAll('.toast-notification');
                    existingToasts.forEach(toast => toast.remove());

                    const toast = document.createElement('div');
                    toast.className = `toast-notification fixed top-4 right-4 z-50 max-w-sm w-full bg-white border border-gray-200 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
                    
                    const typeClasses = {
                        success: 'border-green-200 bg-green-50',
                        error: 'border-red-200 bg-red-50',
                        warning: 'border-yellow-200 bg-yellow-50',
                        info: 'border-blue-200 bg-blue-50'
                    };
                    
                    const typeIcons = {
                        success: 'fa-check-circle text-green-600',
                        error: 'fa-exclamation-circle text-red-600',
                        warning: 'fa-exclamation-triangle text-yellow-600',
                        info: 'fa-info-circle text-blue-600'
                    };

                    toast.className += ` ${typeClasses[type] || typeClasses.info}`;

                    toast.innerHTML = `
                        <div class="p-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas ${typeIcons[type] || typeIcons.info}"></i>
                                </div>
                                <div class="ml-3 w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900">${message}</p>
                                </div>
                                <div class="ml-4 flex-shrink-0 flex">
                                    <button class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
                                        <i class="fas fa-times text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(toast);

                    // Animate in
                    setTimeout(() => {
                        toast.classList.remove('translate-x-full');
                    }, 100);

                    // Auto remove
                    if (duration > 0) {
                        setTimeout(() => {
                            toast.classList.add('translate-x-full');
                            setTimeout(() => toast.remove(), 300);
                        }, duration);
                    }
                }

                // Get URL parameters
                static getUrlParams() {
                    const params = new URLSearchParams(window.location.search);
                    const result = {};
                    for (const [key, value] of params) {
                        result[key] = value;
                    }
                    return result;
                }

                // Update URL parameter without page reload
                static updateUrlParam(key, value) {
                    const url = new URL(window.location);
                    if (value) {
                        url.searchParams.set(key, value);
                    } else {
                        url.searchParams.delete(key);
                    }
                    window.history.replaceState({}, '', url);
                }

                // Escape HTML
                static escapeHtml(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                }

                // Generate random ID
                static generateId(prefix = 'id') {
                    return `${prefix}_${Math.random().toString(36).substr(2, 9)}`;
                }

                // Check if element is in viewport
                static isInViewport(element) {
                    const rect = element.getBoundingClientRect();
                    return (
                        rect.top >= 0 &&
                        rect.left >= 0 &&
                        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                    );
                }

                // Smooth scroll to element
                static scrollToElement(element, offset = 0) {
                    const elementPosition = element.offsetTop - offset;
                    window.scrollTo({
                        top: elementPosition,
                        behavior: 'smooth'
                    });
                }

                // Local storage helpers
                static setLocalStorage(key, value) {
                    try {
                        localStorage.setItem(key, JSON.stringify(value));
                        return true;
                    } catch (error) {
                        console.error('Error saving to localStorage:', error);
                        return false;
                    }
                }

                static getLocalStorage(key, defaultValue = null) {
                    try {
                        const item = localStorage.getItem(key);
                        return item ? JSON.parse(item) : defaultValue;
                    } catch (error) {
                        console.error('Error reading from localStorage:', error);
                        return defaultValue;
                    }
                }

                static removeLocalStorage(key) {
                    try {
                        localStorage.removeItem(key);
                        return true;
                    } catch (error) {
                        console.error('Error removing from localStorage:', error);
                        return false;
                    }
                }
            }

            // Make Utils available globally
            window.Utils = Utils;

            // Initialize page utilities when DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
                // Update last updated timestamp
                const lastUpdatedElement = document.getElementById('last-updated');
                if (lastUpdatedElement) {
                    lastUpdatedElement.textContent = 'Just now';
                }

                // Add keyboard shortcuts
                document.addEventListener('keydown', function(e) {
                    // Ctrl/Cmd + K to focus search
                    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                        e.preventDefault();
                        const searchInput = document.getElementById('database-search');
                        if (searchInput) {
                            searchInput.focus();
                            searchInput.select();
                        }
                    }
                    
                    // Ctrl/Cmd + N to create new database
                    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                        e.preventDefault();
                        if (typeof showCreateDatabaseModal === 'function') {
                            showCreateDatabaseModal();
                        }
                    }
                });
            });
        ]]></script>
    </t>
</templates>
