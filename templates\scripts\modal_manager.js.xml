<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Modal Management JavaScript -->
    <t t-name="scripts.modal_manager">
        <script><![CDATA[
            // Modal Manager Class
            class ModalManager {
                constructor() {
                    this.activeModal = null;
                    this.initializeEventListeners();
                }

                initializeEventListeners() {
                    // Handle escape key to close modals
                    document.addEventListener('keydown', (e) => {
                        if (e.key === 'Escape' && this.activeModal) {
                            this.hideModal(this.activeModal);
                        }
                    });

                    // Handle backdrop clicks
                    document.addEventListener('click', (e) => {
                        if (e.target.classList.contains('modal-backdrop')) {
                            this.hideModal(e.target.id);
                        }
                    });
                }

                showModal(modalId) {
                    const modal = document.getElementById(modalId);
                    if (!modal) {
                        console.error(`Modal with id '${modalId}' not found`);
                        return;
                    }

                    // Hide any currently active modal
                    if (this.activeModal && this.activeModal !== modalId) {
                        this.hideModal(this.activeModal);
                    }

                    modal.classList.remove('hidden');
                    modal.classList.add('flex');
                    this.activeModal = modalId;

                    // Focus first input if available
                    const firstInput = modal.querySelector('input, select, textarea');
                    if (firstInput) {
                        setTimeout(() => firstInput.focus(), 100);
                    }

                    // Prevent body scroll
                    document.body.style.overflow = 'hidden';
                }

                hideModal(modalId) {
                    const modal = document.getElementById(modalId);
                    if (!modal) {
                        console.error(`Modal with id '${modalId}' not found`);
                        return;
                    }

                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    
                    if (this.activeModal === modalId) {
                        this.activeModal = null;
                    }

                    // Reset form if it exists
                    const form = modal.querySelector('form');
                    if (form) {
                        form.reset();
                    }

                    // Restore body scroll
                    document.body.style.overflow = '';
                }

                isModalOpen(modalId) {
                    const modal = document.getElementById(modalId);
                    return modal && !modal.classList.contains('hidden');
                }
            }

            // Global modal manager instance
            const modalManager = new ModalManager();

            // Global modal functions for backward compatibility
            function showCreateDatabaseModal() {
                modalManager.showModal('createDatabaseModal');
            }

            function hideCreateDatabaseModal() {
                modalManager.hideModal('createDatabaseModal');
            }

            function showDatabaseInfoModal() {
                modalManager.showModal('databaseInfoModal');
            }

            function hideDatabaseInfoModal() {
                modalManager.hideModal('databaseInfoModal');
            }

            // Database menu toggle functionality
            function toggleDatabaseMenu(button) {
                const menu = button.nextElementSibling;
                const allMenus = document.querySelectorAll('[data-menu="true"]');
                
                // Close all other menus
                allMenus.forEach(m => {
                    if (m !== menu) {
                        m.classList.add('hidden');
                    }
                });
                
                // Toggle current menu
                menu.classList.toggle('hidden');
                
                // Close menu when clicking outside
                if (!menu.classList.contains('hidden')) {
                    const closeMenu = (e) => {
                        if (!button.contains(e.target) && !menu.contains(e.target)) {
                            menu.classList.add('hidden');
                            document.removeEventListener('click', closeMenu);
                        }
                    };
                    setTimeout(() => document.addEventListener('click', closeMenu), 0);
                }
            }

            // Show database information in modal
            function showDatabaseInfo(dbName) {
                const modal = document.getElementById('databaseInfoModal');
                const content = document.getElementById('databaseInfoContent');
                
                if (!modal || !content) {
                    console.error('Database info modal elements not found');
                    return;
                }

                // Find database data
                const dbCard = document.querySelector(`[data-dbname="${dbName}"]`);
                if (!dbCard) {
                    console.error(`Database card for '${dbName}' not found`);
                    return;
                }

                // Extract database information
                const dbInfo = {
                    name: dbName,
                    status: dbCard.querySelector('.bg-green-100, .bg-yellow-100') ? 
                           (dbCard.querySelector('.bg-green-100') ? 'Ready' : 'Not Initialized') : 'Unknown',
                    created: 'Unknown',
                    size: 'Unknown',
                    users: '0'
                };

                // Populate modal content
                content.innerHTML = `
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 gap-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 mb-2">Database Details</h4>
                                <dl class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600">Name:</dt>
                                        <dd class="font-medium">${dbInfo.name}</dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600">Status:</dt>
                                        <dd class="font-medium">${dbInfo.status}</dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600">Created:</dt>
                                        <dd class="font-medium">${dbInfo.created}</dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600">Size:</dt>
                                        <dd class="font-medium">${dbInfo.size}</dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600">Users:</dt>
                                        <dd class="font-medium">${dbInfo.users}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                        
                        <div class="flex space-x-3 pt-4 border-t border-gray-200">
                            <button type="button" 
                                    class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors" 
                                    onclick="hideDatabaseInfoModal(); connectToDatabase('${dbName}')">
                                Connect to Database
                            </button>
                            <button type="button" 
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors" 
                                    onclick="hideDatabaseInfoModal()">
                                Close
                            </button>
                        </div>
                    </div>
                `;

                modalManager.showModal('databaseInfoModal');
            }
        ]]></script>
    </t>
</templates>
