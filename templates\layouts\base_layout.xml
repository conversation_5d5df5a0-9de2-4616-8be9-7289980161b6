<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Base Layout Template for ERP System -->
    <t t-name="base_layout">
        <html>
            <head>
                <title t-esc="title or 'ERP System'"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                
                <!-- External Dependencies -->
                <script src="https://cdn.tailwindcss.com"></script>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
                
                <!-- Tailwind Configuration -->
                <t t-call="styles.tailwind_config"/>
                
                <!-- Page-specific styles -->
                <t t-if="additional_styles">
                    <t t-raw="additional_styles"/>
                </t>
            </head>
            <body class="bg-gray-50 font-sans">
                <!-- Header Section -->
                <t t-if="show_header != False">
                    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex items-center justify-between h-14 sm:h-16">
                                <!-- Logo and Title -->
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
                                            <i class="fas fa-database text-white text-sm sm:text-base"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h1 class="text-lg sm:text-xl font-bold text-gray-900" t-esc="header_title or title or 'ERP System'"/>
                                        <p t-if="header_subtitle" class="text-xs sm:text-sm text-gray-500 hidden sm:block" t-esc="header_subtitle"/>
                                    </div>
                                </div>
                                
                                <!-- Header Actions -->
                                <div class="flex items-center space-x-2">
                                    <t t-if="header_actions">
                                        <t t-raw="header_actions"/>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </header>
                </t>

                <!-- Main Content Area -->
                <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
                    <!-- Page Content -->
                    <t t-if="content">
                        <t t-raw="content"/>
                    </t>
                    
                    <!-- Default slot for template inheritance -->
                    <t t-slot="main_content"/>
                </main>

                <!-- Footer Section -->
                <t t-if="show_footer != False">
                    <footer class="text-center py-8 text-gray-500 text-sm border-t border-gray-200 mt-12">
                        <p t-esc="footer_text or 'ERP System v1.0 | Powered by FastAPI &amp; PostgreSQL | Built with modern web technologies'"/>
                        <t t-if="footer_extra">
                            <t t-raw="footer_extra"/>
                        </t>
                    </footer>
                </t>

                <!-- Page-specific scripts -->
                <t t-if="additional_scripts">
                    <t t-raw="additional_scripts"/>
                </t>
                
                <!-- Default slot for scripts -->
                <t t-slot="scripts"/>
            </body>
        </html>
    </t>
</templates>
