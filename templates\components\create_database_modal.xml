<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Create Database Modal Component -->
    <t t-name="components.create_database_modal">
        <div id="createDatabaseModal" class="hidden fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 items-center justify-center p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-slide-up">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                        <i class="fas fa-plus-circle text-blue-600"></i>
                        <span>Create New Database</span>
                    </h3>
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-md p-1" 
                            onclick="hideCreateDatabaseModal()">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <!-- Modal Body -->
                <div class="p-6">
                    <form id="createDatabaseForm" onsubmit="handleCreateDatabase(event)">
                        <!-- Database Name -->
                        <div class="mb-4">
                            <label for="dbName" class="block text-sm font-medium text-gray-700 mb-2">
                                Database Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="dbName" 
                                   name="name" 
                                   required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm" 
                                   placeholder="Enter database name"
                                   pattern="[a-zA-Z0-9_\-]+"
                                   title="Only letters, numbers, underscores, and hyphens are allowed"/>
                            <p class="mt-1 text-xs text-gray-500">Only letters, numbers, underscores, and hyphens are allowed</p>
                        </div>
                        
                        <!-- Language Selection -->
                        <div class="mb-4">
                            <label for="dbLanguage" class="block text-sm font-medium text-gray-700 mb-2">
                                Language
                            </label>
                            <select id="dbLanguage" 
                                    name="language" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm">
                                <option value="en_US">English (US)</option>
                                <option value="en_GB">English (UK)</option>
                                <option value="fr_FR">French</option>
                                <option value="de_DE">German</option>
                                <option value="es_ES">Spanish</option>
                                <option value="pt_BR">Portuguese (Brazil)</option>
                                <option value="zh_CN">Chinese (Simplified)</option>
                                <option value="ja_JP">Japanese</option>
                            </select>
                        </div>
                        
                        <!-- Demo Data Option -->
                        <div class="mb-6">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" 
                                       id="loadDemo" 
                                       name="demo" 
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 transition-colors"/>
                                <span class="text-sm text-gray-700">Install demo data</span>
                            </label>
                            <p class="mt-1 text-xs text-gray-500 ml-6">Includes sample data for testing and learning</p>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="flex space-x-3 pt-4 border-t border-gray-200">
                            <button type="button" 
                                    class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors" 
                                    onclick="hideCreateDatabaseModal()">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors flex items-center justify-center space-x-2">
                                <i class="fas fa-plus text-sm"></i>
                                <span>Create Database</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </t>

    <!-- Database Info Modal Component -->
    <t t-name="components.database_info_modal">
        <div id="databaseInfoModal" class="hidden fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 items-center justify-center p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto animate-slide-up">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                        <i class="fas fa-info-circle text-blue-600"></i>
                        <span>Database Information</span>
                    </h3>
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-md p-1" 
                            onclick="hideDatabaseInfoModal()">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
                
                <!-- Modal Body -->
                <div class="p-6">
                    <div id="databaseInfoContent">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
